* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* Safe area support for notch and punch-hole displays */
html {
    /* Ensure full coverage including notch areas */
    width: 100vw;
    height: 100vh;
    height: 100dvh; /* Dynamic viewport height for mobile */
    /* Prevent automatic orientation changes */
    -ms-touch-action: manipulation;
    touch-action: manipulation;
}

/* Support for devices with notches/punch holes */
@supports (padding: max(0px)) {
    body {
        /* Add padding for safe areas while allowing background to extend */
        padding-left: max(0px, env(safe-area-inset-left));
        padding-right: max(0px, env(safe-area-inset-right));
        padding-top: max(0px, env(safe-area-inset-top));
        padding-bottom: max(0px, env(safe-area-inset-bottom));
    }
}

/* Ensure game works well in both portrait and landscape */
@media screen and (orientation: portrait) {
    .casino-container {
        /* Optimize for portrait mode */
        height: 100dvh;
        overflow-y: auto;
        overflow-x: hidden;
    }

    .casino-table {
        padding: 10px;
    }

    /* Adjust layout for portrait mode */
    .top-status {
        position: relative;
        margin-bottom: 10px;
    }

    .deck-area {
        position: relative;
        margin-bottom: 15px;
    }

    .dealer-section {
        margin-bottom: 20px;
    }

    .bottom-controls {
        position: relative;
    }
}



body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Microsoft YaHei', Roboto, 'Helvetica Neue', Arial, sans-serif;
    background:
        url('/blackjack-online/images/backImg.webp') center center / cover no-repeat;
    color: #ffffff;
    min-height: 100dvh;
    overflow: hidden;
    position: relative;
}

body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    pointer-events: none;
    z-index: 1;
}

.casino-container {
    width: 100vw;
    height: 100dvh;
    position: relative;
    z-index: 2;
    display: flex;
    flex-direction: column;
}

.left-controls {
    display: flex;
    align-items: center;
    gap: 10px;
}

/* Button styles moved to common-buttons.css */

.settings-button.disabled,
.settings-button:disabled {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.4), rgba(75, 85, 99, 0.4));
    border-color: rgba(156, 163, 175, 0.2);
    color: rgba(156, 163, 175, 0.6);
    cursor: not-allowed;
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.settings-button.disabled:hover,
.settings-button:disabled:hover {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.4), rgba(75, 85, 99, 0.4));
    transform: none;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.rules-icon {
    font-size: 14px;
}

.rules-text {
    font-size: 11px;
}





@media (max-width: 768px) {

    .current-setting-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }

    .setting-label,
    .setting-value {
        font-size: 13px;
    }

    .settings-notice {
        padding: 10px;
        margin-top: 12px;
    }

    .notice-text {
        font-size: 13px;
    }、

    .game-actions-section {
        gap: 8px;
    }

    .game-actions-section .action-btn {
        width: 45px;
        height: 45px;
        padding: 0;
    }
}

.rules-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    z-index: 1000;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
    box-sizing: border-box;
}

.rules-modal-content {
    background: linear-gradient(145deg, #1f2937, #111827);
    border-radius: 16px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    max-width: 600px;
    max-height: 80vh;
    width: 100%;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.rules-header {
    background: linear-gradient(145deg, #ffd700, #ffed4e);
    color: #1f2937;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid rgba(255, 215, 0, 0.3);
}

.rules-header h2 {
    margin: 0;
    font-size: 24px;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.rules-close {
    background: none;
    border: none;
    font-size: 28px;
    font-weight: bold;
    color: #1f2937;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.rules-close:hover {
    background: rgba(31, 41, 55, 0.1);
    transform: scale(1.1);
}

.rules-body {
    padding: 20px;
    max-height: calc(80vh - 100px);
    overflow-y: auto;
    color: #e5e7eb;
}

.rules-section {
    margin-bottom: 24px;
}

.rules-section:last-child {
    margin-bottom: 0;
}

.rules-section h3 {
    color: #ffd700;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 12px 0;
    display: flex;
    align-items: center;
    gap: 8px;
}

.rules-section p {
    margin: 0 0 12px 0;
    line-height: 1.6;
    color: #d1d5db;
}

.rules-section ul {
    margin: 0;
    padding-left: 20px;
    list-style-type: none;
}

.rules-section li {
    margin-bottom: 8px;
    line-height: 1.5;
    position: relative;
    padding-left: 16px;
}

.rules-section li:before {
    content: '';
    color: #ffd700;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.rules-section li strong {
    color: #fbbf24;
}

.rules-section ul ul {
    margin-top: 8px;
    padding-left: 16px;
}

.rules-section ul ul li {
    color: #9ca3af;
    font-size: 14px;
}

.rules-section ul ul li:before {
    content: '';
    color: #6b7280;
}

.deck-area {
    position: absolute;
    top:10px;
    left: 0;
    right: 0;
    z-index: 50;
    display: flex;
    justify-content: space-between;
    padding: 0 30px;
    pointer-events: none;
    max-width: 500px;
    margin: 0 auto;
}

.deck-container {
    pointer-events: auto;
}

.deck-pile {
    text-align: center;
    position: relative;
}

.deck-card {
    width: 60px;
    height: 84px;
    margin: 0 auto;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    position: relative;
}

.discard-card {
    opacity: 0.6;
    filter: grayscale(0.3);
}

.deck-info {
    position: absolute;
    bottom: 0;
    left: 0;
    color: #ffffff;
    text-align: center;
    width: 100%;
    padding: 5px;
}

.deck-label {
    font-size: 10px;
    color: #ffd700;
    font-weight: 600;
    margin-bottom: 3px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
}

.deck-count {
    font-size: 16px;
    font-weight: bold;
    color: #ffffff;
    margin-bottom: 5px;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
}

.progress-bar {
    width: 100%;
    height: 6px;
    background: rgba(0, 0, 0, 0.4);
    border-radius: 3px;
    overflow: hidden;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #10b981, #059669);
    border-radius: 2px;
    transition: width 0.5s ease;
    box-shadow: 0 0 4px rgba(16, 185, 129, 0.3);
}

.discard-fill {
    background: linear-gradient(90deg, #ef4444, #dc2626);
    box-shadow: 0 0 4px rgba(239, 68, 68, 0.3);
}

@keyframes deckShuffle {

    0%,
    100% {
        transform: translateY(0);
    }

    25% {
        transform: translateY(-3px) rotateZ(1deg);
    }

    50% {
        transform: translateY(-1px) rotateZ(-1deg);
    }

    75% {
        transform: translateY(-2px) rotateZ(0.5deg);
    }
}

.deck-card.shuffling {
    animation: deckShuffle 0.5s ease-in-out 4;
}

.fullscreen-icon {
    font-size: 16px;
    line-height: 1;
}

.home-icon {
    line-height: 35px;
    color: #fbbf24;
}

.audio-button {
    background: rgba(0, 0, 0, 0.6);
    padding: 8px 12px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    display: flex;
    align-items: center;
    justify-content: center;
    width:35px;
    height: 35px;
}

.audio-button:hover {
    background: rgba(0, 0, 0, 0.8);
    transform: scale(1.1);
}

.audio-icon {
    font-size: 18px;
    line-height: 35px;
}

.audio-settings-content {
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
}

.audio-control-group {
    display: flex;
    flex-direction: column;
    gap: 24px;
    margin-bottom: 24px;
}

.audio-control-item {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 12px;
    padding: 20px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.audio-control-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
}

.audio-control-icon {
    font-size: 24px;
    margin-right: 12px;
}

.audio-control-label {
    font-size: 18px;
    font-weight: 600;
    color: white;
    flex: 1;
}

.audio-toggle-btn {
    background: rgba(34, 197, 94, 0.2);
    border: 1px solid rgba(34, 197, 94, 0.4);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
}

.audio-toggle-btn:hover {
    background: rgba(34, 197, 94, 0.3);
    transform: scale(1.05);
}

.audio-toggle-btn.muted {
    background: rgba(239, 68, 68, 0.2);
    border-color: rgba(239, 68, 68, 0.4);
}

.audio-toggle-btn.muted:hover {
    background: rgba(239, 68, 68, 0.3);
}

.toggle-icon {
    font-size: 16px;
    color: white;
}

.volume-control {
    display: flex;
    align-items: center;
    gap: 12px;
}

.volume-label {
    font-size: 14px;
    color: rgba(255, 255, 255, 0.8);
    min-width: 50px;
}

.volume-slider {
    flex: 1;
    height: 6px;
    border-radius: 3px;
    background: rgba(255, 255, 255, 0.2);
    outline: none;
    -webkit-appearance: none;
    appearance: none;
}

.volume-slider::-webkit-slider-thumb {
    -webkit-appearance: none;
    appearance: none;
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #ffd700;
    cursor: pointer;
    border: 2px solid rgba(255, 215, 0, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.volume-slider::-moz-range-thumb {
    width: 18px;
    height: 18px;
    border-radius: 50%;
    background: #ffd700;
    cursor: pointer;
    border: 2px solid rgba(255, 215, 0, 0.8);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
}

.volume-value {
    font-size: 14px;
    color: #ffd700;
    font-weight: 600;
    min-width: 40px;
    text-align: right;
}

.audio-test-section {
    border-top: 1px solid rgba(255, 255, 255, 0.1);
    padding-top: 20px;
}

.audio-test-section h4 {
    color: white;
    font-size: 16px;
    margin-bottom: 12px;
    text-align: center;
}

.test-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
}

.test-btn {
    background: linear-gradient(145deg, #3b82f6, #2563eb);
    color: white;
    border: none;
    border-radius: 8px;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
}

.test-btn:active {
    transform: translateY(0);
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .audio-settings-content {
        width: 95%;
        margin: 20px auto;
    }

    .audio-control-item {
        padding: 16px;
    }

    .audio-control-header {
        flex-wrap: wrap;
        gap: 8px;
    }

    .audio-control-label {
        font-size: 16px;
    }

    .volume-control {
        flex-wrap: wrap;
        gap: 8px;
    }

    .test-buttons {
        flex-direction: column;
        align-items: center;
    }

    .test-btn {
        width: 120px;
    }
}

.balance-display {
    background: rgba(0, 0, 0, 0.6);
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    color: #ffd700;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.casino-table {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
}

.casino-table::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
}

.dealer-section {
    position: absolute;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
    z-index: 10;
    display: none;

}

.dealer-section.show {
    display: block;
}

.dealer-message {
    background: rgba(0, 0, 0, 0.8);
    color: #ffd700;
    padding: 8px 20px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 15px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.dealer-cards-container {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
}

.dealer-cards-area {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    min-height: 80px;
    margin-top: 10px;
    position: relative;
}

.dealer-cards-area .card {
    position: relative;
    margin: 0 -15px;
    transition: all 0.3s ease;
}

.dealer-cards-area .card:nth-child(1) {
    z-index: 1;
}

.dealer-cards-area .card:nth-child(2) {
    z-index: 2;
}

.dealer-cards-area .card:nth-child(3) {
    z-index: 3;
}

.dealer-cards-area .card:nth-child(4) {
    z-index: 4;
}

.dealer-cards-area .card:nth-child(5) {
    z-index: 5;
}

.dealer-cards-area .card:nth-child(6) {
    z-index: 6;
}

.dealer-cards-area .card:nth-child(7) {
    z-index: 7;
}

.dealer-cards-area .card:nth-child(8) {
    z-index: 8;
}

.dealer-cards-area .card:nth-child(9) {
    z-index: 9;
}

.dealer-cards-area .card:nth-child(10) {
    z-index: 10;
}


.dealer-section.bust .dealer-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/bust.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
}

.dealer-section.lost .dealer-cards-area .card {
    filter: grayscale(1) brightness(0.5);
    transition: all 0.5s ease;
}

.dealer-section.lost .dealer-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/defeat.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
    transform: translateX(-50%);
}

.dealer-section.won .dealer-cards-area .card {
    transition: all 0.5s ease;
}

.dealer-section.won .dealer-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/win.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: winImageBounce 0.5s ease-in-out;
    pointer-events: none;
}

.dealer-section.blackjack .dealer-cards-area .card {
    filter: brightness(1.3) saturate(1.5);
    box-shadow: 0 0 25px rgba(255, 0, 255, 0.8);
    transition: all 0.5s ease;
}

.dealer-section.blackjack .dealer-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    background-image: url('/blackjack-online/images/blackJack.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: blackjackImageBounce 0.5s ease-in-out;
    pointer-events: none;
}

.dealer-section.twenty-one .dealer-cards-area .card {
    filter: brightness(1.3) saturate(1.4);
    box-shadow: 0 0 25px rgba(0, 255, 127, 0.8);
    transition: all 0.5s ease;
}

.dealer-section.twenty-one .dealer-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 110px;
    height: 110px;
    background-image: url('/blackjack-online/images/21.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: twentyOneImageBounce 0.6s ease-in-out;
    pointer-events: none;
}

.dealer-info {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.dealer-avatar {
    display: none;
}

.dealer-title {
    font-size: 16px;
    color: #ffd700;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
    text-align: center;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.8);
}

.insurance-panel {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(145deg, #1a1a2e, #16213e);
    border: 3px solid #ffd700;
    border-radius: 16px;
    padding: 20px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.8);
    z-index: 1000;
    min-width: 300px;
    text-align: center;
}

.insurance-content {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.insurance-title {
    font-size: 20px;
    color: #ffd700;
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.insurance-description {
    font-size: 14px;
    color: #ffffff;
    line-height: 1.4;
}

.insurance-amount {
    font-size: 16px;
    color: #ffd700;
    font-weight: 600;
}

.insurance-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
}

.insurance-btn {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.insurance-btn.buy-btn {
    background: linear-gradient(145deg, #22c55e, #16a34a);
    color: white;
    box-shadow: 0 4px 12px rgba(34, 197, 94, 0.3);
}

.insurance-btn.buy-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(34, 197, 94, 0.4);
}

.insurance-btn.decline-btn {
    background: linear-gradient(145deg, #ef4444, #dc2626);
    color: white;
    box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
}

.insurance-btn.decline-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(239, 68, 68, 0.4);
}

.action-btn.surrender-btn:disabled {
    background: rgba(128, 128, 128, 0.3);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.action-btn.surrender-btn:disabled:hover {
    transform: none;
    box-shadow: none;
}

.player-position.surrendered .card {
    filter: grayscale(1) brightness(0.6);
    transition: all 0.5s ease;
}

.player-position.surrendered::after {
    content: "SURRENDERED";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 255, 255, 0.9);
    color: #dc2626;
    font-weight: 700;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    letter-spacing: 1px;
    z-index: 10;
}

.insurance-stack {
    position: absolute;
    top: -10px;
    right: -10px;
    z-index: 15;
}

.insurance-stack .bet-chip-preview {
    border: 2px solid #3b82f6;
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.6);
    position: relative;
}

.insurance-stack .bet-chip-preview::after {
    content: "INS";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 8px;
    font-weight: 800;
    color: #3b82f6;
    text-shadow: 0 0 2px rgba(255, 255, 255, 0.8);
    z-index: 1;
}

.dealer-avatar img {
    width: 50px;
    height: 50px;
    border-radius: 50%;
    border: 3px solid #ffd700;
    background: linear-gradient(145deg, #2c3e50, #34495e);
    padding: 2px;
    filter: brightness(1.1) contrast(1.1);
    box-shadow:
        0 0 15px rgba(255, 215, 0, 0.4),
        0 0 25px rgba(255, 215, 0, 0.2),
        inset 0 0 10px rgba(255, 215, 0, 0.1);
}

.dealer-name {
    font-size: 12px;
    color: #ffd700;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 1px;
}

.table-center {
    position: absolute;
    top: 40%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    z-index: 5;
}

.game-settings-container {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 20px;
}

.settings-panel {
    background: linear-gradient(145deg,
            rgba(26, 26, 26, 0.95) 0%,
            rgba(40, 40, 40, 0.9) 100%);
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 20px;
    padding: 30px;
    box-shadow:
        0 10px 30px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(255, 215, 0, 0.2),
        inset 0 2px 10px rgba(255, 255, 255, 0.1);
    text-align: center;
    min-width: 400px;
}

.settings-title {
    color: #ffd700;
    font-size: 28px;
    font-weight: bold;
    margin-bottom: 30px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}



.setting-group {
    margin-bottom: 25px;
}

.setting-label {
    display: block;
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

.setting-options {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.setting-btn {
    background: linear-gradient(145deg,
            rgba(60, 60, 60, 0.9) 0%,
            rgba(40, 40, 40, 0.9) 100%);
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    padding: 15px 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    color: #ffffff;
    font-weight: 600;
    min-width: 80px;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 1px 3px rgba(255, 255, 255, 0.1);
}

.setting-btn:hover {
    transform: translateY(-2px);
    border-color: rgba(255, 215, 0, 0.5);
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 15px rgba(255, 215, 0, 0.3),
        inset 0 1px 3px rgba(255, 255, 255, 0.2);
}

.setting-btn.active {
    background: linear-gradient(145deg,
            rgba(255, 215, 0, 0.9) 0%,
            rgba(255, 193, 7, 0.8) 100%);
    border-color: rgba(255, 215, 0, 0.8);
    color: #1a1a1a;
    box-shadow:
        0 6px 12px rgba(0, 0, 0, 0.4),
        0 0 20px rgba(255, 215, 0, 0.5),
        inset 0 2px 6px rgba(255, 255, 255, 0.3);
}

.setting-btn.active:hover {
    transform: translateY(-2px);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.5),
        0 0 25px rgba(255, 215, 0, 0.6),
        inset 0 2px 8px rgba(255, 255, 255, 0.4);
}

.option-number {
    display: block;
    font-size: 24px;
    font-weight: bold;
    line-height: 1;
}

.option-text {
    display: block;
    font-size: 14px;
    margin-top: 5px;
    opacity: 0.9;
}

.start-game-container {
    display: flex;
    justify-content: center;
    align-items: center;
}

.start-game-btn {
    background: linear-gradient(145deg,
            rgba(255, 215, 0, 0.95) 0%,
            rgba(255, 193, 7, 0.9) 50%,
            rgba(255, 152, 0, 0.85) 100%);
    border: 3px solid rgba(255, 215, 0, 0.8);
    border-radius: 20px;
    padding: 20px 40px;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 215, 0, 0.4),
        inset 0 2px 10px rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 200px;
    animation: startBtnPulse 2s ease-in-out infinite;
}

.start-game-btn::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg,
            rgba(255, 215, 0, 0.8),
            rgba(255, 193, 7, 0.9),
            rgba(255, 235, 59, 0.8),
            rgba(255, 215, 0, 0.8));
    background-size: 300% 300%;
    border-radius: 20px;
    z-index: -1;
    animation: startBtnRunningLight 2s linear infinite;
}

.start-game-btn:hover {
    transform: scale(1.05);
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(255, 215, 0, 0.6),
        inset 0 3px 15px rgba(255, 255, 255, 0.4);
}

.start-game-btn:active {
    transform: scale(0.98);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        0 0 15px rgba(255, 215, 0, 0.5),
        inset 0 2px 8px rgba(255, 255, 255, 0.2);
}

.btn-text {
    font-size: 12px;
    font-weight: 900;
    color: #fff;
    text-shadow: 0 2px 4px rgba(255, 255, 255, 0.5);
}

.btn-subtitle {
    font-size: 14px;
    font-weight: 600;
    color: rgba(26, 26, 26, 0.8);
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
    letter-spacing: 1px;
}

@keyframes startBtnPulse {

    0%,
    100% {
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(255, 215, 0, 0.4),
            inset 0 2px 10px rgba(255, 255, 255, 0.3);
    }

    50% {
        box-shadow:
            0 12px 24px rgba(0, 0, 0, 0.4),
            0 0 35px rgba(255, 215, 0, 0.7),
            inset 0 3px 15px rgba(255, 255, 255, 0.4);
    }
}

@keyframes startBtnRunningLight {
    0% {
        background-position: 0% 0%;
    }

    100% {
        background-position: 300% 300%;
    }
}

.table-logo {
    margin-bottom: 20px;
}

.blackjack-text {
    font-size: 24px;
    font-weight: 900;
    color: #ffd700;
    text-shadow:
        0 0 10px rgba(255, 215, 0, 0.5),
        2px 2px 4px rgba(0, 0, 0, 0.5);
    letter-spacing: 2px;
    margin-bottom: 5px;
}

.payout-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    letter-spacing: 1px;
}

.game-status {
    color: #ffffff;
    padding: 8px 16px;
    font-size: 14px;
    font-weight: 600;
}

.players-area {
    position: absolute;
    bottom: 120px;
    left: 50%;
    transform: translateX(-50%);
    display: none;

    justify-content: space-between;
    gap: 30px;
    z-index: 10;
    width: 90%;
    max-width: 1000px;
}

.players-area.show {
    display: flex;
}

.players-area.player-count-1 {
    justify-content: center;
    width: 100%;
}

.players-area.player-count-1 .player-position {
    margin: 0 auto;
}

.players-area.player-count-4 {
    width: 95%;
}

.players-area.player-count-6 {
    gap: 15px;
    width: 98%;
    max-width: 1580px;
}

.players-area.player-count-6 .player-position {
    margin: 0 -8px;
}

.player-position {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-width: 140px;
    opacity: 0.8;
    transition: all 0.3s ease;
    position: relative;
}

.player-position.current-player {
    opacity: 1;
}

.player-position.active {
    opacity: 1;
}

.player-cards-area {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    min-height: 92px;
    height: 92px;
    position: relative;
    order: 2;
    overflow: visible;
}

.player-cards-area .card {
    position: relative;
    margin: 0 -15px;
    transition: all 0.3s ease;
}

.player-cards-area .card:nth-child(1) {
    z-index: 1;
}

.player-cards-area .card:nth-child(2) {
    z-index: 2;
}

.player-cards-area .card:nth-child(3) {
    z-index: 3;
}

.player-cards-area .card:nth-child(4) {
    z-index: 4;
}

.player-cards-area .card:nth-child(5) {
    z-index: 5;
}

.player-cards-area .card:nth-child(6) {
    z-index: 6;
}

.player-cards-area .card:nth-child(7) {
    z-index: 7;
}

.player-cards-area .card:nth-child(8) {
    z-index: 8;
}

.player-cards-area .card:nth-child(9) {
    z-index: 9;
}

.bet-spot {
    position: relative;
}

.bet-circle {
    width: 60px;
    height: 60px;
    border: 2px solid rgba(255, 215, 0, 0.4);
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    cursor: pointer;
    position: relative;
    overflow: visible;
}

.bet-circle.active {
    border-color: rgba(255, 215, 0, 0.8);
    box-shadow: 0 0 15px rgba(255, 215, 0, 0.3);
}

.bet-circle:hover {
    border-color: rgba(255, 215, 0, 0.8);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.3);
}

.bet-circle.has-bet {
    border-color: rgba(255, 215, 0, 1);
    box-shadow: 0 0 25px rgba(255, 215, 0, 0.5);
}

.bet-amount {
    color: #ffd700;
    font-weight: 700;
    font-size: 11px;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.5);
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    z-index: 10;
    white-space: nowrap;
}

.bet-chips-stack {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -40%);
    width: 50px;
    height: 50px;
    z-index: 5;
    display: flex;
    justify-content: center;
}

.bet-chip-preview {
    position: absolute;
    width: 35px;
    height: 35px;
    border-radius: 50%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    transition: all 0.3s ease;
}

.bet-chip-preview:nth-child(1) {
    top: 0;
    z-index: 1;

}

.bet-chip-preview:nth-child(2) {
    top: -4px;
    z-index: 2;
}

.bet-chip-preview:nth-child(3) {
    top: -8px;
    z-index: 3;
}

.bet-chip-preview:nth-child(4) {
    top: -12px;
    z-index: 4;
}

.bet-chip-preview:nth-child(5) {
    top: -16px;
    z-index: 5;
}

.bet-chip-preview:nth-child(n+6) {
    top: -20px;
    z-index: 6;

}

.bet-chip-preview.chip-1-preview {
    background-image: url('/blackjack-online/images/chips/chips1-preview.svg');
}

.bet-chip-preview.chip-2-preview {
    background-image: url('/blackjack-online/images/chips/chips2-preview.svg');
}

.bet-chip-preview.chip-3-preview {
    background-image: url('/blackjack-online/images/chips/chips3-preview.svg');
}

.bet-chip-preview.chip-4-preview {
    background-image: url('/blackjack-online/images/chips/chips4-preview.svg');
}

.bet-chip-preview.chip-5-preview {
    background-image: url('/blackjack-online/images/chips/chips5-preview.svg');
}

.bet-chip-preview.chip-6-preview {
    background-image: url('/blackjack-online/images/chips/chips6-preview.svg');
}

.bet-chip-preview.chip-7-preview {
    background-image: url('/blackjack-online/images/chips/chips7-preview.svg');
}

.bet-chip-preview.chip-8-preview {
    background-image: url('/blackjack-online/images/chips/chips8-preview.svg');
}

.bet-chip-preview.chip-9-preview {
    background-image: url('/blackjack-online/images/chips/chips9-preview.svg');
}



.player-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    color: #ffffff;
    font-size: 12px;
    font-weight: 600;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    position: relative;
    margin-bottom: 8px;
}

.player-avatar {
    display: none;
}

.player-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 30%;
    border: none;
    filter: brightness(1.1) contrast(1.1);
    transition: all 0.3s ease;
}

.player-avatar .player-name {
    position: absolute;
    bottom: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 11px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
}

.player-chips {
    background: rgba(0, 0, 0, 0.7);
    color: #fbbf24;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 11px;
    font-weight: 700;
    margin-top: 5px;
    border: 1px solid rgba(251, 191, 36, 0.3);
}

.player-name {
    margin-bottom: 3px;
    font-size: 13px;
}

.player-avatar {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 3px;
}

.current-player .player-avatar img {
    border: 3px solid #ffd700;
    box-shadow:
        0 0 20px rgba(255, 215, 0, 0.8),
        0 0 40px rgba(255, 215, 0, 0.4),
        inset 0 0 10px rgba(255, 255, 255, 0.2);
    animation: humanPlayerGlow 2s ease-in-out infinite alternate;
}

.player-name {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.8);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.current-player .player-name {
    color: #ffd700;
}

.player-position.active-turn .player-avatar {
    position: relative;
    animation: activeTurnPulse 2s ease-in-out infinite;
}

.player-position.active-turn .player-avatar::before {
    content: '';
    position: absolute;
    top: -8px;
    left: -8px;
    right: -8px;
    bottom: -8px;
    border-radius: 50%;
    background: linear-gradient(45deg,
            rgba(255, 215, 0, 0.8),
            rgba(255, 193, 7, 0.9),
            rgba(255, 235, 59, 0.8),
            rgba(255, 215, 0, 0.8));
    background-size: 300% 300%;
    animation: avatarRunningLight 2s linear infinite;
    z-index: -1;
}

.player-position.active-turn .player-avatar img {
    border: 3px solid rgba(255, 215, 0, 0.9);
    box-shadow:
        0 0 15px rgba(255, 215, 0, 0.8),
        inset 0 0 10px rgba(255, 215, 0, 0.3);
    animation: avatarGlow 1.5s ease-in-out infinite alternate;
}

@keyframes activeTurnPulse {

    0%,
    100% {
        transform: scale(1);
    }

    50% {
        transform: scale(1.05);
    }
}

@keyframes avatarRunningLight {
    0% {
        background-position: 0% 0%;
    }

    100% {
        background-position: 300% 300%;
    }
}

@keyframes avatarGlow {
    0% {
        box-shadow:
            0 0 15px rgba(255, 215, 0, 0.8),
            inset 0 0 10px rgba(255, 215, 0, 0.3);
    }

    100% {
        box-shadow:
            0 0 25px rgba(255, 215, 0, 1),
            0 0 35px rgba(255, 215, 0, 0.6),
            inset 0 0 15px rgba(255, 215, 0, 0.5);
    }
}

@keyframes humanPlayerGlow {
    0% {
        box-shadow:
            0 0 20px rgba(255, 215, 0, 0.8),
            0 0 40px rgba(255, 215, 0, 0.4),
            inset 0 0 10px rgba(255, 255, 255, 0.2);
    }

    100% {
        box-shadow:
            0 0 30px rgba(255, 215, 0, 1),
            0 0 60px rgba(255, 215, 0, 0.6),
            inset 0 0 15px rgba(255, 255, 255, 0.3);
    }
}

.player-cards-container {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    position: relative;
    margin-bottom: 5px;
}

.player-score {
    background: rgba(0, 0, 0, 0.6);
    color: #ffffff;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 700;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-bottom: 8px;
    order: 1;
    opacity: 1;
    transition: opacity 0.3s ease;
}

.player-score.hidden {
    opacity: 0;
    visibility: hidden;
}

.player-score span.hidden {
    display: none;
}

.player-balance {
    background: rgba(0, 0, 0, 0.7);
    color: #fbbf24;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 700;
    display: inline-block;
    border: 1px solid rgba(251, 191, 36, 0.3);
    min-width: 60px;
    text-align: center;
}

.dealer-score {
    background: rgba(0, 0, 0, 0.6);
    color: #ffffff;
    padding: 4px 12px;
    border-radius: 15px;
    font-size: 14px;
    font-weight: 700;
    border: 1px solid rgba(255, 255, 255, 0.2);
    margin-top: 8px;
}

.betting-countdown {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.9), rgba(255, 193, 7, 0.8));
    border: 2px solid #ffd700;
    border-radius: 20px;
    padding: 15px 20px;
    margin-top: 15px;
    text-align: center;
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.3),
        0 0 20px rgba(255, 215, 0, 0.4);
    animation: countdownPulse 1s ease-in-out infinite alternate;
}

.countdown-text {
    color: #1a1a1a;
    font-size: 16px;
    font-weight: 700;
    margin-bottom: 8px;
    text-shadow: 0 1px 2px rgba(255, 255, 255, 0.5);
}

.countdown-timer {
    color: #1a1a1a;
    font-size: 32px;
    font-weight: 900;
    text-shadow:
        0 2px 4px rgba(255, 255, 255, 0.5),
        0 0 10px rgba(255, 215, 0, 0.8);
    animation: timerPulse 1s ease-in-out infinite;
}

@keyframes countdownPulse {
    0% {
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(255, 215, 0, 0.4);
    }

    100% {
        box-shadow:
            0 12px 24px rgba(0, 0, 0, 0.4),
            0 0 30px rgba(255, 215, 0, 0.6);
    }
}

@keyframes timerPulse {
    0% {
        transform: scale(1);
        text-shadow:
            0 2px 4px rgba(255, 255, 255, 0.5),
            0 0 10px rgba(255, 215, 0, 0.8);
    }

    50% {
        transform: scale(1.1);
        text-shadow:
            0 2px 4px rgba(255, 255, 255, 0.7),
            0 0 15px rgba(255, 215, 0, 1),
            0 0 25px rgba(255, 215, 0, 0.6);
    }

    100% {
        transform: scale(1);
        text-shadow:
            0 2px 4px rgba(255, 255, 255, 0.5),
            0 0 10px rgba(255, 215, 0, 0.8);
    }
}

.betting-countdown.warning {
    background: linear-gradient(145deg, rgba(255, 152, 0, 0.9), rgba(255, 111, 0, 0.8));
    border-color: #ff9800;
    animation: warningPulse 0.5s ease-in-out infinite alternate;
}

.betting-countdown.critical {
    background: linear-gradient(145deg, rgba(244, 67, 54, 0.9), rgba(211, 47, 47, 0.8));
    border-color: #f44336;
    animation: criticalPulse 0.5s ease-in-out infinite alternate;
}

@keyframes warningPulse {
    0% {
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.3),
            0 0 20px rgba(255, 152, 0, 0.5);
    }

    100% {
        box-shadow:
            0 12px 24px rgba(0, 0, 0, 0.4),
            0 0 35px rgba(255, 152, 0, 0.8);
    }
}

@keyframes criticalPulse {
    0% {
        box-shadow:
            0 8px 16px rgba(0, 0, 0, 0.3),
            0 0 25px rgba(244, 67, 54, 0.6);
        transform: scale(1);
    }

    100% {
        box-shadow:
            0 15px 30px rgba(0, 0, 0, 0.5),
            0 0 40px rgba(244, 67, 54, 1);
        transform: scale(1.05);
    }
}



.player-position.bust .player-cards-area {
    transition: all 0.5s ease;
}

.player-position.bust .player-cards-area .card {
    filter: grayscale(1) brightness(0.4);
    transition: all 0.5s ease;
}

.player-position.bust .player-avatar img {
    filter: grayscale(1) brightness(0.5);
    transition: all 0.5s ease;
}

.player-position.bust .player-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/bust.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
}

@keyframes bustImageShake {

    0%,
    100% {
        transform: translateX(-50%) rotate(0deg);
    }

    25% {
        transform: translateX(-50%) rotate(-3deg);
    }

    75% {
        transform: translateX(-50%) rotate(3deg);
    }
}

.player-position.lost .player-cards-area .card {
    filter: grayscale(1) brightness(0.5);
    transition: all 0.5s ease;
}

.player-position.lost .player-avatar img {
    filter: grayscale(1) brightness(0.6);
}

.player-position.lost .player-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/defeat.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
    transform: translateX(-50%);
}

@keyframes loseImageFade {

    0% {
        transform: translateX(-50%) scale(1.4);
        opacity: 0.5;
    }

    50% {
        transform: translateX(-50%) scale(1.2);
        opacity: 0.7;
    }

    100% {
        transform: translateX(-50%) scale(1);
        opacity: 1;
    }
}

.player-position.won .player-cards-area .card {
    transition: all 0.5s ease;
}

.player-position.won .player-avatar img {
    filter: brightness(1.1) saturate(1.2);
}

.player-position.won .player-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/win.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: winImageBounce 0.5s ease-in-out;
    pointer-events: none;
}

@keyframes winImageBounce {

    0%,
    100% {
        transform: translateX(-50%) scale(1);
    }

    25% {
        transform: translateX(-50%) scale(1.1);
    }

    50% {
        transform: translateX(-50%) scale(1.2);
    }

    75% {
        transform: translateX(-50%) scale(1.1);
    }
}

.player-position.blackjack .player-cards-area .card {
    filter: brightness(1.3) saturate(1.5);
    box-shadow: 0 0 25px rgba(255, 0, 255, 0.8);
    transition: all 0.5s ease;
}

.player-position.blackjack .player-avatar img {
    filter: brightness(1.2) saturate(1.4);
}

.player-position.blackjack .player-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 120px;
    height: 120px;
    background-image: url('/blackjack-online/images/blackJack.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: blackjackImageBounce 0.5s ease-in-out;
    pointer-events: none;
}

@keyframes blackjackImageBounce {
    0% {
        transform: translateX(-50%) scale(0) rotate(-180deg);
        opacity: 0;
    }

    50% {
        transform: translateX(-50%) scale(1.3) rotate(0deg);
        opacity: 1;
    }

    100% {
        transform: translateX(-50%) scale(1) rotate(0deg);
        opacity: 1;
    }
}

.player-position.twenty-one .player-cards-area .card {
    filter: brightness(1.3) saturate(1.4);
    box-shadow: 0 0 25px rgba(0, 255, 127, 0.8);
    transition: all 0.5s ease;
}

.player-position.twenty-one .player-avatar img {
    filter: brightness(1.2) saturate(1.3);
}

.player-position.twenty-one .player-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 110px;
    height: 110px;
    background-image: url('/blackjack-online/images/21.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: twentyOneImageBounce 0.6s ease-in-out;
    pointer-events: none;
}

@keyframes twentyOneImageBounce {
    0% {
        transform: translateX(-50%) scale(0) rotateY(-90deg);
        opacity: 0;
    }

    60% {
        transform: translateX(-50%) scale(1.2) rotateY(0deg);
        opacity: 1;
    }

    100% {
        transform: translateX(-50%) scale(1) rotateY(0deg);
        opacity: 1;
    }
}

.player-position.push .player-cards-area .card {
    filter: brightness(1.1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    transition: all 0.5s ease;
}

.player-position.push .player-cards-container::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/push.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
}

@keyframes pushImageFloat {

    0%,
    100% {
        transform: translateX(-50%) scale(1) translateY(0px);
    }

    50% {
        transform: translateX(-50%) scale(1.05) translateY(-5px);
    }
}

@keyframes cardDeal {
    0% {
        transform: translateY(-30px) rotateY(180deg) scale(0.8);
        opacity: 0;
    }

    50% {
        transform: translateY(-5px) rotateY(90deg) scale(0.9);
        opacity: 0.5;
    }

    100% {
        transform: translateY(0) rotateY(0deg) scale(1);
        opacity: 1;
    }
}

.card-back {
    background: linear-gradient(145deg, #1e40af, #3b82f6);
    position: relative;
    overflow: hidden;
}

.error-card {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    border: 2px solid #991b1b;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    animation: errorPulse 1s infinite alternate;
}

@keyframes errorPulse {
    0% {
        opacity: 0.7;
    }

    100% {
        opacity: 1;
    }
}

.card-back::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 30px;
    height: 30px;
    background: repeating-linear-gradient(45deg,
            #ffffff20,
            #ffffff20 2px,
            transparent 2px,
            transparent 4px);
    transform: translate(-50%, -50%);
    border-radius: 3px;
}

.card-corner {
    position: absolute;
    display: flex;
    flex-direction: column;
    align-items: center;
    font-weight: 900;
    line-height: 1;
}

.card-corner.top-left {
    top: 3px;
    left: 3px;
}

.card-corner.bottom-right {
    bottom: 3px;
    right: 3px;
    transform: rotate(180deg);
}

.card-center {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    display: flex;
    align-items: center;
    justify-content: center;
}

.card-value {
    font-size: 0.6rem;
    line-height: 1;
    font-weight: 900;
    margin-bottom: 1px;
}

.card-suit {
    font-size: 0.5rem;
    line-height: 1;
}

.card-suit-large {
    font-size: 1.2rem;
    opacity: 0.3;
}

.card.red {
    color: #dc2626;
}

.card.red .card-suit-large {
    color: #dc2626;
}

.card.black {
    color: #1f2937;
}

.card.black .card-suit-large {
    color: #1f2937;
}

.bottom-controls {
    position: absolute;
    bottom: 40px;
    left: 0;
    right: 0;
    z-index: 20;
    opacity: 1;
    display: flex;
    flex-direction: column;
    transition: all 0.2s ease;
}

.bottom-controls.show {
    opacity: 1;
}

.chip-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 5px;
    padding: 5px 0;
    max-width: 95%;
    margin: 0 auto;
}

.chip-tray {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.betting-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 10px;
}

.game-actions-section {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
    padding: 4px 0;
}

#betting-controls {
    display: none;
}

.chip-btn {
    background: none;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    padding: 0;
}

.chip {
    width: 43px;
    height: 43px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: 900;
    font-size: 11px;
    color: #ffffff;
    text-shadow: 0 0 2px rgba(0, 0, 0, 1);
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
    position: relative;
    transition: all 0.3s ease;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    border: none;
    background-color: #fff;
}

.chip-1 {
    background-image: url('/blackjack-online/images/chips/chips1.svg');
}

.chip-2 {
    background-image: url('/blackjack-online/images/chips/chips2.svg');
}

.chip-3 {
    background-image: url('/blackjack-online/images/chips/chips3.svg');
}

.chip-4 {
    background-image: url('/blackjack-online/images/chips/chips4.svg');
}

.chip-5 {
    background-image: url('/blackjack-online/images/chips/chips5.svg');
}

.chip-6 {
    background-image: url('/blackjack-online/images/chips/chips6.svg');
}

.chip-7 {
    background-image: url('/blackjack-online/images/chips/chips7.svg');
}

.chip-8 {
    background-image: url('/blackjack-online/images/chips/chips8.svg');
}

.chip-9 {
    background-image: url('/blackjack-online/images/chips/chips9.svg');
}



.chip-btn:hover .chip {
    transform: translateY(-3px) scale(1.1);
    box-shadow:
        0 8px 16px rgba(0, 0, 0, 0.4),
        inset 0 2px 4px rgba(255, 255, 255, 0.3);
}

.chip-btn.disabled,
.chip-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
}

.chip-btn.disabled:hover .chip,
.chip-btn:disabled:hover .chip {
    transform: none;
    box-shadow:
        0 4px 8px rgba(0, 0, 0, 0.3),
        inset 0 2px 4px rgba(255, 255, 255, 0.2);
}

.chip-btn.disabled .chip,
.chip-btn:disabled .chip {
    filter: grayscale(30%);
}

.chip-btn.selected .chip {
    transform: translateY(-5px) scale(1.15);
    box-shadow:
        0 12px 24px rgba(0, 0, 0, 0.5),
        0 0 20px rgba(255, 215, 0, 0.4);
}

.action-btn {
    padding: 8px;
    border: none;
    border-radius: 8px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    color: #ffffff;
}

.clear-btn {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.clear-btn.disabled,
.clear-btn:disabled {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.3), rgba(75, 85, 99, 0.3));
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    opacity: 0.4;
    filter: grayscale(50%);
}

.clear-btn.disabled:hover,
.clear-btn:disabled:hover {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.3), rgba(75, 85, 99, 0.3));
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}



.deal-btn {
    background: linear-gradient(145deg, #10b981, #059669);
    border: 1px solid rgba(16, 185, 129, 0.3);
    color: #ffffff;
    font-weight: 700;
    animation: dealPulse 2s ease-in-out infinite;
}

.deal-btn.disabled,
.deal-btn:disabled {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.3), rgba(75, 85, 99, 0.3));
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.4);
    animation: none;
    cursor: not-allowed;
    opacity: 0.4;
    filter: grayscale(50%);
}

.deal-btn.disabled:hover,
.deal-btn:disabled:hover {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.3), rgba(75, 85, 99, 0.3));
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.double-bet-btn {
    background: linear-gradient(145deg, #f59e0b, #d97706);
    border: 1px solid rgba(245, 158, 11, 0.3);
    color: #ffffff;
    font-weight: 700;
}

.double-bet-btn.disabled,
.double-bet-btn:disabled {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.3), rgba(75, 85, 99, 0.3));
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: rgba(255, 255, 255, 0.4);
    cursor: not-allowed;
    opacity: 0.4;
    filter: grayscale(50%);
}

.double-bet-btn.disabled:hover,
.double-bet-btn:disabled:hover {
    background: linear-gradient(145deg, rgba(107, 114, 128, 0.3), rgba(75, 85, 99, 0.3));
    transform: none;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

@keyframes dealPulse {

    0%,
    100% {
        box-shadow: 0 4px 12px rgba(16, 185, 129, 0.3);
    }

    50% {
        box-shadow: 0 6px 20px rgba(16, 185, 129, 0.6);
    }
}

.action-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.action-controls,
.game-actions-section {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    margin-bottom: 15px;
}

.action-controls {
    display: none;
}

.action-controls .action-btn,
.game-actions-section .action-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    width: 45px;
    height: 45px;
    border-radius: 50%;
    font-size: 20px;
    line-height: 1;
    transition: all 0.3s ease;
    position: relative;
    border: 3px solid rgba(255, 255, 255, 0.3);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    padding: 0;
}

/* Special fix for square symbol (■) centering - desktop only */
@media (min-width: 769px) {
    .stand-btn .btn-icon {
        transform: translateY(-1px);
    }
}

.start-game-btn .btn-text {
    font-size: 24px;
    color: #1a1a1a;
}

.hint-btn {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border: 3px solid #fbbf24;
    box-shadow: 0 0 15px rgba(251, 191, 36, 0.4);
}

.double-btn {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border: 3px solid #fe9801;
    box-shadow: 0 0 15px rgba(254, 152, 1, 0.4);
}

.split-btn {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border: 3px solid #2196f3;
    box-shadow: 0 0 15px rgba(33, 150, 243, 0.4);
}

.hit-btn {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border: 3px solid #4cb04f;
    box-shadow: 0 0 15px rgba(76, 176, 79, 0.4);
}

.stand-btn {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border: 3px solid #f44235;
    box-shadow: 0 0 15px rgba(244, 66, 53, 0.4);
}

.insurance-btn {
    background: linear-gradient(145deg, #2c3e50, #34495e);
    border: 3px solid #ffffff;
    box-shadow: 0 0 15px rgba(255, 255, 255, 0.4);
}

.action-controls .action-btn:hover,
.game-actions-section .action-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.action-controls .hint-btn:hover,
.game-actions-section .hint-btn:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 25px rgba(251, 191, 36, 0.6);
}

.action-controls .double-btn:hover,
.game-actions-section .double-btn:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 25px rgba(245, 158, 11, 0.6);
}

.action-controls .split-btn:hover,
.game-actions-section .split-btn:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 25px rgba(59, 130, 246, 0.6);
}

.action-controls .hit-btn:hover,
.game-actions-section .hit-btn:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 25px rgba(16, 185, 129, 0.6);
}

.action-controls .stand-btn:hover,
.game-actions-section .stand-btn:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 25px rgba(239, 68, 68, 0.6);
}

.action-controls .insurance-btn:hover,
.game-actions-section .insurance-btn:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4), 0 0 25px rgba(255, 255, 255, 0.6);
}

.action-btn.hint-highlight {
    animation: hintPulse 1.5s ease-in-out infinite;
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6);
}

@keyframes hintPulse {
    0%, 100% {
        box-shadow: 0 0 20px rgba(255, 215, 0, 0.8), 0 0 40px rgba(255, 215, 0, 0.6);
    }
    50% {
        box-shadow: 0 0 30px rgba(255, 215, 0, 1), 0 0 60px rgba(255, 215, 0, 0.8);
    }
}

.action-controls .action-btn:disabled,
.game-actions-section .action-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
    transform: none;
    filter: grayscale(50%);
}



.action-controls .btn-text,
.game-actions-section .btn-text {
    position: absolute;
    bottom: -25px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 10px;
    font-weight: 600;
    color: #ffffff;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.8);
    white-space: nowrap;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}





.btn-text {
    font-weight: 600;
    letter-spacing: 0.5px;
}

.card {
    width: 70px;
    height: 95px;
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    padding: 4px;
    font-weight: bold;
    position: relative;
    transition: all 0.3s ease;
    transform-style: preserve-3d;
    animation: cardDeal 0.5s ease-out;
}

@keyframes cardDeal {
    0% {
        transform: translateY(-100px) rotateY(180deg);
        opacity: 0;
    }

    50% {
        transform: translateY(-20px) rotateY(90deg);
        opacity: 0.5;
    }

    100% {
        transform: translateY(0) rotateY(0deg);
        opacity: 1;
    }
}

.card.red {
    color: #dc2626;
}

.card.black {
    color: #1f2937;
}

.card-back {
    background: linear-gradient(145deg, #1e40af, #3b82f6);
    position: relative;
    overflow: hidden;
}

.card-back::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 40px;
    height: 40px;
    background: repeating-linear-gradient(45deg,
            #ffffff20,
            #ffffff20 2px,
            transparent 2px,
            transparent 6px);
    transform: translate(-50%, -50%);
    border-radius: 4px;
}

.card-value {
    font-size: 1.2rem;
    line-height: 1;
    font-weight: 600;
}

.card-suit {
    font-size: 1.4rem;
    text-align: center;
    margin: auto;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    animation: modalFadeIn 0.3s ease-out;
    justify-content: center;
    align-items: center;
}

.modal.show {
    display: flex;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.modal-content {
    background: linear-gradient(145deg, #1e293b, #334155);
    margin: 0;
    padding: 0;
    border-radius: 15px;
    width: 90%;
    max-width: 400px;
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1);
    overflow: hidden;
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    padding: 20px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-icon {
    font-size: 2.5rem;
    margin-bottom: 10px;
    display: block;
}

.modal-header h2 {
    color: #ffd700;
    font-size: 1.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.modal-body {
    padding: 20px;
}

.result-message {
    font-size: 1.1rem;
    color: #e5e7eb;
    margin-bottom: 15px;
    text-align: center;
}

.result-stats {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.stat-row {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    color: #e5e7eb;
    font-weight: 500;
}

.stat-row.highlight {
    background: linear-gradient(145deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
    border-color: rgba(16, 185, 129, 0.3);
    color: #10b981;
    font-weight: 700;
}

.modal-footer {
    padding: 15px 20px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-btn {
    width: 100%;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(145deg, #059669, #047857);
    color: #ffffff;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 0.9rem;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.7);
    z-index: 999;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-content {
    text-align: center;
    color: #ffffff;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid rgba(255, 255, 255, 0.3);
    border-top: 4px solid #ffd700;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 15px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.loading-text {
    font-size: 1.1rem;
    font-weight: 600;
    color: #ffd700;
}

.action-buttons {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    margin-bottom: 20px;
}

.game-btn {
    padding: 15px 20px;
    border: none;
    border-radius: 12px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    font-size: 0.9rem;
    position: relative;
    overflow: hidden;
}

.deal-btn {
    background: linear-gradient(145deg, #059669, #047857);
    color: #ffffff;
    border: 1px solid rgba(16, 185, 129, 0.3);
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.hit-btn {
    background: linear-gradient(145deg, #4cb04f, #3a8a3d);
    color: #ffffff;
    border: 1px solid rgba(76, 176, 79, 0.3);
    box-shadow: 0 4px 15px rgba(76, 176, 79, 0.3);
}

.stand-btn {
    background: linear-gradient(145deg, #f44235, #d32f2f);
    color: #ffffff;
    border: 1px solid rgba(244, 66, 53, 0.3);
    box-shadow: 0 4px 15px rgba(244, 66, 53, 0.3);
}

.hint-btn {
    background: linear-gradient(145deg, #fbbf24, #f59e0b);
    color: #ffffff;
    border: 1px solid rgba(251, 191, 36, 0.3);
    box-shadow: 0 4px 15px rgba(251, 191, 36, 0.3);
}

.double-btn {
    background: linear-gradient(145deg, #fe9801, #e6890a);
    color: #ffffff;
    border: 1px solid rgba(254, 152, 1, 0.3);
    box-shadow: 0 4px 15px rgba(254, 152, 1, 0.3);
}

.game-btn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow:
        0 8px 25px rgba(0, 0, 0, 0.3),
        0 4px 15px rgba(255, 255, 255, 0.1);
}

.game-btn:disabled {
    opacity: 0.4;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.secondary-actions {
    display: flex;
    gap: 12px;
}

.secondary-btn {
    flex: 1;
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    background: linear-gradient(145deg, #6b7280, #4b5563);
    color: #ffffff;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 6px;
    font-size: 0.8rem;
}

.secondary-btn:hover {
    transform: translateY(-2px);
    background: linear-gradient(145deg, #4b5563, #374151);
}

.shortcuts-panel {
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
    padding: 20px;
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.1);
    margin-top: 25px;
}

.shortcuts-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin-bottom: 15px;
    color: #ffd700;
    font-weight: 600;
}

.shortcuts-icon {
    font-size: 1.2rem;
}

.shortcuts-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 10px;
}

.shortcut-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.shortcut-item:hover {
    background: rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
}

kbd {
    background: linear-gradient(145deg, #374151, #1f2937);
    color: #ffd700;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: 600;
    border: 1px solid rgba(255, 215, 0, 0.3);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.shortcut-item span {
    font-size: 0.75rem;
    color: #94a3b8;
    font-weight: 500;
}

/* Second modal style - already updated above */

@keyframes modalSlideIn {
    from {
        transform: translateY(-50px);
        opacity: 0;
    }

    to {
        transform: translateY(0);
        opacity: 1;
    }
}

.modal-header {
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
    padding: 25px;
    text-align: center;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.result-icon {
    font-size: 3rem;
    margin-bottom: 10px;
    display: block;
}

.modal-header h2 {
    color: #ffd700;
    font-size: 1.8rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 0 10px rgba(255, 215, 0, 0.3);
}

.modal-body {
    padding: 25px;
}

.result-message {
    font-size: 1.2rem;
    color: #e5e7eb;
    margin-bottom: 20px;
    text-align: center;
}

.result-stats {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 15px;
    background: rgba(0, 0, 0, 0.3);
    border-radius: 10px;
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.stat-item.highlight {
    background: linear-gradient(145deg, rgba(16, 185, 129, 0.2), rgba(16, 185, 129, 0.1));
    border-color: rgba(16, 185, 129, 0.3);
}

.stat-label {
    color: #94a3b8;
    font-weight: 500;
}

.stat-value {
    color: #ffffff;
    font-weight: 700;
    font-size: 1.1rem;
}

.stat-item.highlight .stat-value {
    color: #10b981;
    text-shadow: 0 0 10px rgba(16, 185, 129, 0.3);
}

.modal-footer {
    padding: 20px 25px;
    background: rgba(0, 0, 0, 0.2);
    border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.modal-btn {
    width: 100%;
    padding: 15px 25px;
    border: none;
    border-radius: 10px;
    font-weight: 700;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    font-size: 1rem;
}

.modal-btn.primary {
    background: linear-gradient(145deg, #059669, #047857);
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(5, 150, 105, 0.3);
}

.modal-btn.secondary {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    color: #ffffff;
}

.modal-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.rules-content {
    max-width: 600px;
}

.rules-section {
    margin-bottom: 20px;
}

.rules-section h3 {
    color: #ffd700;
    font-size: 1.1rem;
    margin-bottom: 10px;
    font-weight: 600;
}

.rules-section p {
    color: #e5e7eb;
    line-height: 1.6;
    margin-bottom: 10px;
}

.rules-section ul {
    list-style: none;
    padding: 0;
}

.rules-section li {
    color: #e5e7eb;
    padding: 5px 0;
    padding-left: 20px;
    position: relative;
    line-height: 1.5;
}

.rules-section li::before {
    content: '';
    color: #ffd700;
    font-weight: bold;
    position: absolute;
    left: 0;
}

.split-hands-container {
    position: relative;
    width: 100%;
    height: 100%;
}

.current-hand {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    position: relative;
}

.current-hand .card {
    margin-left: -15px;
}

.current-hand .card:first-child {
    margin-left: 0;
}

.hand-indicator {
    position: absolute;
    top: -25px;
    left: 50%;
    transform: translateX(-50%);
    background: #fbbf24;
    color: #000;
    padding: 4px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    z-index: 10;
}

.hand-indicator.active {
    background: #10b981;
    color: white;
    animation: pulse 2s infinite;
}

@keyframes pulse {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.7;
    }
}

.mini-hand {
    position: absolute;
    top: -10px;
    left: -80px;
    transform: scale(0.5);
    transform-origin: top left;
    display: flex;
    flex-wrap: wrap;
    z-index: 5;
}

.mini-card {
    margin-left: -12px;
    width: 50px;
    height: 70px;
    font-size: 8px;
    animation: none !important;
    transform: none !important;
}

.mini-card:first-child {
    margin-left: 1px;
}

.mini-hand .card.dealt {
    animation: none !important;
    transform: none !important;
}

.split-hand-score {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: #ffd700;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    z-index: 10;
    pointer-events: none;
}

.split-hand-score.current-score {
    bottom: -25px;
    right: 0;
    font-size: 14px;
    padding: 3px 8px;
}

.split-hand-score.mini-score {
    bottom: -20px;
    right: -5px;
    font-size: 10px;
    padding: 1px 4px;
    transform: scale(0.8);
}

.mini-hand-indicator {
    position: absolute;
    top: -15px;
    left: 0;
    background: #475569;
    color: white;
    padding: 2px 6px;
    border-radius: 6px;
    font-size: 8px;
    font-weight: bold;
    white-space: nowrap;
}

.side-bets {
    display: flex;
    gap: 10px;
    margin-top: 10px;
    justify-content: center;
}

.side-bet-spot {
    background: rgba(0, 0, 0, 0.3);
    border: 2px solid #334155;
    border-radius: 50%;
    padding: 8px;
    min-width: 80px;
    text-align: center;
    transition: all 0.3s ease;
    position: absolute;
    bottom: -20%;
    display: flex;
    justify-content: center;
    align-items: center;
    text-align: center;
    flex-direction: column;
}

.perfectPairs {
    left: -120%;
}

.twentyOnePlusThree {
    right: -120%;
}

.side-bet-spot.has-bet {
    border-color: #fbbf24;
    background: rgba(251, 191, 36, 0.2);
}

.side-bet-chips-stack {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 25px;
    height: 25px;
    z-index: 5;
    display: flex;
    justify-content: center;
}

.side-bet-chip-preview {
    position: absolute;
    width: 25px;
    height: 25px;
    border-radius: 50%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    transition: all 0.3s ease;
}

.side-bet-chip-preview:nth-child(1) {
    top: 0;
    z-index: 1;
}

.side-bet-chip-preview:nth-child(2) {
    top: -3px;
    z-index: 2;
}

.side-bet-chip-preview:nth-child(3) {
    top: -6px;
    z-index: 3;
}

.side-bet-chip-preview:nth-child(4) {
    top: -9px;
    z-index: 4;
}

.side-bet-chip-preview:nth-child(5) {
    top: -12px;
    z-index: 5;
}

.side-bet-chip-preview:nth-child(n+6) {
    top: -15px;
    z-index: 6;
}

.side-bet-chip-preview.chip-1-preview {
    background-image: url('/blackjack-online/images/chips/chips1-preview.svg');
}

.side-bet-chip-preview.chip-2-preview {
    background-image: url('/blackjack-online/images/chips/chips2-preview.svg');
}

.side-bet-chip-preview.chip-3-preview {
    background-image: url('/blackjack-online/images/chips/chips3-preview.svg');
}

.side-bet-chip-preview.chip-4-preview {
    background-image: url('/blackjack-online/images/chips/chips4-preview.svg');
}

.side-bet-chip-preview.chip-5-preview {
    background-image: url('/blackjack-online/images/chips/chips5-preview.svg');
}

.side-bet-chip-preview.chip-6-preview {
    background-image: url('/blackjack-online/images/chips/chips6-preview.svg');
}

.side-bet-chip-preview.chip-7-preview {
    background-image: url('/blackjack-online/images/chips/chips7-preview.svg');
}

.side-bet-chip-preview.chip-8-preview {
    background-image: url('/blackjack-online/images/chips/chips8-preview.svg');
}

.side-bet-chip-preview.chip-9-preview {
    background-image: url('/blackjack-online/images/chips/chips9-preview.svg');
}

.flying-chip {
    position: fixed;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-size: contain;
    background-position: center;
    background-repeat: no-repeat;
    z-index: 1000;
    pointer-events: none;
    transition: none;
}

.flying-chip.chip-1-preview {
    background-image: url('/blackjack-online/images/chips/chips1-preview.svg');
}

.flying-chip.chip-2-preview {
    background-image: url('/blackjack-online/images/chips/chips2-preview.svg');
}

.flying-chip.chip-3-preview {
    background-image: url('/blackjack-online/images/chips/chips3-preview.svg');
}

.flying-chip.chip-4-preview {
    background-image: url('/blackjack-online/images/chips/chips4-preview.svg');
}

.flying-chip.chip-5-preview {
    background-image: url('/blackjack-online/images/chips/chips5-preview.svg');
}

.flying-chip.chip-6-preview {
    background-image: url('/blackjack-online/images/chips/chips6-preview.svg');
}

.flying-chip.chip-7-preview {
    background-image: url('/blackjack-online/images/chips/chips7-preview.svg');
}

.flying-chip.chip-8-preview {
    background-image: url('/blackjack-online/images/chips/chips8-preview.svg');
}

.flying-chip.chip-9-preview {
    background-image: url('/blackjack-online/images/chips/chips9-preview.svg');
}

@keyframes chipToBetFly {
    0% {
        left: var(--start-x);
        top: var(--start-y);
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }

    50% {
        left: calc((var(--start-x) + var(--end-x)) / 2);
        top: calc((var(--start-y) + var(--end-y)) / 2 - 50px);
        transform: scale(1.2) rotate(180deg);
        opacity: 0.9;
    }

    100% {
        left: var(--end-x);
        top: var(--end-y);
        transform: scale(0.8) rotate(360deg);
        opacity: 0;
    }
}

@keyframes chipWinningFly {
    0% {
        left: var(--start-x);
        top: var(--start-y);
        transform: scale(0.8) rotate(0deg);
        opacity: 0;
    }

    20% {
        left: calc(var(--start-x) + (var(--end-x) - var(--start-x)) * 0.2);
        top: calc(var(--start-y) + (var(--end-y) - var(--start-y)) * 0.2 - 30px);
        transform: scale(1.2) rotate(72deg);
        opacity: 1;
    }

    80% {
        left: calc(var(--start-x) + (var(--end-x) - var(--start-x)) * 0.8);
        top: calc(var(--start-y) + (var(--end-y) - var(--start-y)) * 0.8 - 20px);
        transform: scale(1.1) rotate(288deg);
        opacity: 1;
    }

    100% {
        left: var(--end-x);
        top: var(--end-y);
        transform: scale(1) rotate(360deg);
        opacity: 0;
    }
}

@keyframes chipLosingFly {
    0% {
        left: var(--start-x);
        top: var(--start-y);
        transform: scale(1) rotate(0deg);
        opacity: 1;
    }

    20% {
        left: calc(var(--start-x) + (var(--end-x) - var(--start-x)) * 0.2);
        top: calc(var(--start-y) + (var(--end-y) - var(--start-y)) * 0.2 - 40px);
        transform: scale(1.2) rotate(-72deg);
        opacity: 0.9;
    }

    80% {
        left: calc(var(--start-x) + (var(--end-x) - var(--start-x)) * 0.8);
        top: calc(var(--start-y) + (var(--end-y) - var(--start-y)) * 0.8 - 10px);
        transform: scale(0.9) rotate(-288deg);
        opacity: 0.7;
    }

    100% {
        left: var(--end-x);
        top: var(--end-y);
        transform: scale(0.6) rotate(-360deg);
        opacity: 0;
    }
}

@keyframes chipPushBounce {
    0% {
        left: var(--start-x);
        top: var(--start-y);
        transform: scale(0.8) rotate(0deg);
        opacity: 0.5;
    }

    50% {
        left: var(--start-x);
        top: calc(var(--start-y) - 30px);
        transform: scale(1.1) rotate(180deg);
        opacity: 1;
    }

    100% {
        left: var(--start-x);
        top: var(--start-y);
        transform: scale(1) rotate(360deg);
        opacity: 0;
    }
}

.flying-chip.animate-chipToBet {
    animation: chipToBetFly 1s ease-out forwards;
}

.flying-chip.animate-chipWinning {
    animation: chipWinningFly 1s ease-out forwards;
}

.flying-chip.animate-chipLosing {
    animation: chipLosingFly 1s ease-out forwards;
}

.flying-chip.animate-chipPush {
    animation: chipPushBounce 1s ease-out forwards;
}

.split-hands-container.transitioning .current-hand {
    transition: all 0.5s ease-in-out;
    transform: translateX(-100px) scale(0.8);
    opacity: 0.7;
}

.split-hands-container.transitioning .mini-hand {
    transition: all 0.5s ease-in-out;
    transform: translateX(100px) scale(1.2);
    opacity: 1;
}

.splitting-card {
    transition: all 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    z-index: 100 !important;
}

.splitting-card:hover {
    transform: none !important;

}

.current-hand {
    transition: all 0.3s ease-in-out;
}

.mini-hand {
    transition: all 0.3s ease-in-out;
}

.split-hands-final-container {
    display: flex;
    justify-content: center;
    align-items: flex-start;
    gap: 60px;
    width: 100%;
}

.final-hand {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 45%;
    position: relative;
}

.final-hand .player-cards-area {
    display: flex;
    justify-content: center;
    align-items: flex-end;
    min-height: 80px;
    position: relative;
}

.final-hand .card {
    position: relative;
    margin: 0 -15px;
    transition: all 0.3s ease;
}

.current-hand.bust .card,
.mini-hand.bust .card,
.final-hand.bust .card {
    filter: grayscale(1) brightness(0.4);
    transition: all 0.5s ease;
}

.current-hand.won .card,
.mini-hand.won .card,
.final-hand.won .card {
    transition: all 0.5s ease;
}

.current-hand.lost .card,
.mini-hand.lost .card,
.final-hand.lost .card {
    filter: grayscale(1) brightness(0.5);
    transition: all 0.5s ease;
}

.current-hand.push .card,
.mini-hand.push .card,
.final-hand.push .card {
    filter: brightness(1.1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    transition: all 0.5s ease;
}

.current-hand.blackjack .card,
.mini-hand.blackjack .card,
.final-hand.blackjack .card {
    filter: brightness(1.3) saturate(1.5);
    box-shadow: 0 0 25px rgba(255, 0, 255, 0.8);
    transition: all 0.5s ease;
}

.current-hand.twenty-one .card,
.mini-hand.twenty-one .card,
.final-hand.twenty-one .card {
    filter: brightness(1.3) saturate(1.4);
    box-shadow: 0 0 25px rgba(0, 255, 127, 0.8);
    transition: all 0.5s ease;
}

.current-hand.bust::after,
.mini-hand.bust::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    background-image: url('/blackjack-online/images/bust.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
}

.final-hand.bust::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/bust.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
}

.current-hand.twenty-one::after,
.mini-hand.twenty-one::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    background-image: url('/blackjack-online/images/21.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: twentyOneImageBounce 0.6s ease-in-out;
    pointer-events: none;
}

.final-hand.twenty-one::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 110px;
    height: 110px;
    background-image: url('/blackjack-online/images/21.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: twentyOneImageBounce 0.6s ease-in-out;
    pointer-events: none;
}

.current-hand.blackjack::after,
.mini-hand.blackjack::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 80px;
    background-image: url('/blackjack-online/images/blackJack.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: blackjackImageBounce 0.6s ease-in-out;
    pointer-events: none;
}

.final-hand.blackjack::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 110px;
    height: 110px;
    background-image: url('/blackjack-online/images/blackJack.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: blackjackImageBounce 0.6s ease-in-out;
    pointer-events: none;
}

.final-hand.won::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/win.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: winImageBounce 0.5s ease-in-out;
    pointer-events: none;
}

.final-hand.lost::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/defeat.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
}

.final-hand.push::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background-image: url('/blackjack-online/images/push.png');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
    z-index: 100;
    animation: loseImageFade 0.5s ease-in-out;
    pointer-events: none;
}

.split-settlement-container {
    display: flex;
    justify-content: space-around;
    align-items: flex-start;
    width: 100%;
    gap: 20px;
}

.settlement-hand {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
    max-width: 45%;
}

.settlement-hand .player-cards-area {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-bottom: 10px;
}

.settlement-hand .card {
    margin: 0 -15px;
    position: relative;
}

.settlement-score {
    font-weight: bold;
    font-size: 14px;
    color: #fff;
    text-align: center;
}

.settlement-hand.won .card {
    transition: all 0.5s ease;
}

.settlement-hand.lost .card {
    filter: grayscale(1) brightness(0.5);
    transition: all 0.5s ease;
}

.settlement-hand.bust .card {
    filter: grayscale(1) brightness(0.4);
    transition: all 0.5s ease;
}

.settlement-hand.push .card {
    filter: brightness(1.1);
    box-shadow: 0 0 10px rgba(255, 255, 255, 0.3);
    transition: all 0.5s ease;
}

.settlement-hand.blackjack .card {
    filter: brightness(1.3) saturate(1.5);
    box-shadow: 0 0 25px rgba(255, 0, 255, 0.8);
    transition: all 0.5s ease;
}

.settlement-hand.twenty-one .card {
    filter: brightness(1.3) saturate(1.4);
    box-shadow: 0 0 25px rgba(0, 255, 127, 0.8);
    transition: all 0.5s ease;
}

.side-bet-label {
    font-size: 10px;
    color: #94a3b8;
    margin-bottom: 4px;
}

.side-bet-amount {
    font-size: 12px;
    font-weight: bold;
    color: #fbbf24;
    margin-top: auto;
}

.side-bet-controls {
    position: fixed;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    background: rgba(15, 23, 42, 0.95);
    border: 2px solid #334155;
    border-radius: 12px;
    padding: 20px;
    z-index: 1000;
}

.side-bet-section h4 {
    color: #fbbf24;
    margin: 0 0 15px 0;
    text-align: center;
    font-size: 16px;
}

.side-bet-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.side-bet-btn {
    background: linear-gradient(135deg, #1e293b, #334155);
    border: 2px solid #475569;
    color: #e2e8f0;
    padding: 8px 12px;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    white-space: nowrap;
}

.side-bet-btn:hover {
    background: linear-gradient(135deg, #334155, #475569);
    border-color: #fbbf24;
    transform: translateY(-2px);
}

.side-bet-btn:active {
    transform: translateY(0);
}









.game-over-content {
    max-width: 500px;
    text-align: center;
    background: linear-gradient(135deg, #1e293b 0%, #334155 100%);
    border: 2px solid #ef4444;
    border-radius: 20px;
    padding: 0;
    overflow: hidden;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
}

.game-over-header {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    padding: 30px 20px;
    color: white;
    position: relative;
}

.game-over-header h2 {
    margin: 0;
    font-size: 28px;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.game-over-icon {
    font-size: 48px;
    margin-top: 10px;
    animation: bounce 2s infinite;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% {
        transform: translateY(0);
    }
    40% {
        transform: translateY(-10px);
    }
    60% {
        transform: translateY(-5px);
    }
}

.game-over-message {
    padding: 30px 25px;
    color: #e2e8f0;
}

.main-message {
    font-size: 20px;
    font-weight: bold;
    color: #fbbf24;
    margin-bottom: 20px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.encouragement-message {
    font-size: 16px;
    line-height: 1.6;
    margin-bottom: 20px;
    color: #cbd5e1;
}

.tip-message {
    font-size: 14px;
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 10px;
    padding: 15px;
    color: #93c5fd;
    margin-bottom: 0;
}

.game-over-actions {
    padding: 0 25px 30px 25px;
}

.restart-btn {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
    border: none;
    padding: 15px 30px;
    border-radius: 50px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(16, 185, 129, 0.3);
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
    margin: 0 auto;
    min-width: 200px;
}

.restart-btn:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(16, 185, 129, 0.4);
}

.restart-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(16, 185, 129, 0.3);
}

.restart-icon {
    font-size: 20px;
}

@media (max-width: 768px) {
    .game-over-content {
        max-width: 90%;
        margin: 0;
    }

    .game-over-header h2 {
        font-size: 24px;
    }

    .game-over-icon {
        font-size: 40px;
    }

    .main-message {
        font-size: 18px;
    }

    .encouragement-message {
        font-size: 14px;
    }

    .tip-message {
        font-size: 12px;
        padding: 12px;
    }

    .restart-btn {
        font-size: 16px;
        padding: 12px 25px;
        min-width: 180px;
    }
}

.add-to-home-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10001;
    padding: 20px;
}

.add-to-home-content {
    background: linear-gradient(135deg, #1a4b3a 0%, #2d5a4a 100%);
    border-radius: 20px;
    max-width: 400px;
    width: 100%;
    max-height: calc(100dvh - 40px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
    border: 2px solid #ffd700;
    overflow: hidden;
    animation: slideInUp 0.4s ease-out;
    display: flex;
    flex-direction: column;
}

.add-to-home-header {
    background: linear-gradient(135deg, #ffd700 0%, #ffed4e 100%);
    color: #1a4b3a;
    padding: 20px;
    text-align: center;
    position: relative;
    flex-shrink: 0;
}

.add-to-home-header h3 {
    font-size: 20px;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.close-prompt {
    position: absolute;
    top: 15px;
    right: 15px;
    background: none;
    border: none;
    font-size: 24px;
    color: #1a4b3a;
    cursor: pointer;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: background-color 0.2s;
}

.close-prompt:hover {
    background-color: rgba(26, 75, 58, 0.1);
}

.add-to-home-body {
    padding: 25px;
    color: white;
    overflow-y: auto;
    flex: 1;
}

.instruction-step {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    padding: 15px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    border-left: 4px solid #ffd700;
}

.step-icon {
    font-size: 30px;
    margin-right: 15px;
    flex-shrink: 0;
}

.step-text p {
    margin: 0 0 8px 0;
    line-height: 1.4;
}

.step-text p:last-child {
    margin-bottom: 0;
    opacity: 0.9;
    font-size: 14px;
}

.instruction-steps {
    margin: 20px 0;
}

.step {
    display: flex;
    align-items: center;
    margin-bottom: 12px;
    padding: 10px 0;
}

.step-number {
    background: #ffd700;
    color: #1a4b3a;
    width: 24px;
    height: 24px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-weight: bold;
    font-size: 12px;
    margin-right: 12px;
    flex-shrink: 0;
}

.step-desc {
    font-size: 14px;
    line-height: 1.4;
}

.share-icon, .home-icon {
    padding: 2px 6px;
    border-radius: 4px;
    font-weight: bold;
}

.benefits {
    margin-top: 20px;
    padding: 15px;
    background: rgba(255, 215, 0, 0.1);
    border-radius: 10px;
    border: 1px solid rgba(255, 215, 0, 0.3);
}

.benefits p {
    margin: 0 0 10px 0;
    font-weight: 600;
    color: #ffd700;
}

.benefits ul {
    margin: 0;
    padding-left: 20px;
}

.benefits li {
    margin-bottom: 6px;
    font-size: 13px;
    line-height: 1.4;
}

.add-to-home-footer {
    padding: 20px 25px;
    background: rgba(0, 0, 0, 0.2);
    display: flex;
    gap: 10px;
    flex-direction: column;
    flex-shrink: 0;
}

.continue-anyway-btn, .remind-later-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s;
    text-align: center;
}

.continue-anyway-btn {
    background: #ffd700;
    color: #1a4b3a;
}

.continue-anyway-btn:hover {
    background: #ffed4e;
    transform: translateY(-1px);
}

.remind-later-btn {
    background: transparent;
    color: #ffd700;
    border: 2px solid #ffd700;
}

.remind-later-btn:hover {
    background: #ffd700;
    color: #1a4b3a;
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}



@media (max-width: 480px) {
    .add-to-home-content {
        margin: 10px;
        max-width: none;
        max-height: calc(100dvh - 20px);
    }

    .add-to-home-header h3 {
        font-size: 18px;
    }

    .add-to-home-body {
        padding: 20px;
    }

    .step-text p {
        font-size: 14px;
    }

    .benefits li {
        font-size: 12px;
    }

    .add-to-home-footer {
        padding: 15px 20px;
    }
}

@media (max-height: 500px) {
    .add-to-home-content {
        max-height: calc(100dvh - 10px);
    }

    .add-to-home-header {
        padding: 12px 20px;
    }

    .add-to-home-header h3 {
        font-size: 16px;
    }

    .add-to-home-body {
        padding: 15px 20px;
    }

    .instruction-step {
        padding: 10px;
        margin-bottom: 12px;
    }

    .step-icon {
        font-size: 20px;
        margin-right: 12px;
    }

    .step-text p {
        font-size: 13px;
        margin-bottom: 4px;
    }

    .instruction-steps {
        margin: 12px 0;
    }

    .step {
        margin-bottom: 6px;
        padding: 6px 0;
    }

    .step-desc {
        font-size: 12px;
    }

    .benefits {
        margin-top: 12px;
        padding: 10px;
    }

    .benefits p {
        margin-bottom: 8px;
        font-size: 13px;
    }

    .benefits li {
        margin-bottom: 3px;
        font-size: 11px;
    }

    .add-to-home-footer {
        padding: 12px 20px;
    }

    .continue-anyway-btn, .remind-later-btn {
        padding: 8px 16px;
        font-size: 12px;
    }
}
/* Mobile Touch Optimization */
* {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    /* Prevent double-tap zoom on mobile */
    touch-action: manipulation;
}

/* Improve scrolling performance on mobile */
html {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Prevent text selection on game elements */
.casino-container,
.players-area,
.dealer-section,
.bottom-controls,
.chip-tray,
.action-controls {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Share Incentive Modal Styles */
.share-incentive-content {
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.95), rgba(20, 20, 20, 0.95));
    border: 2px solid #ffd700;
    border-radius: 20px;
    padding: 30px;
    max-width: 500px;
    width: 90%;
    text-align: center;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
}

.share-incentive-header {
    margin-bottom: 25px;
}

.share-incentive-header h2 {
    color: #ffd700;
    font-size: 24px;
    margin: 0 0 10px 0;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.5);
}

.share-incentive-icon {
    font-size: 48px;
    margin: 10px 0;
}

.share-incentive-message {
    margin-bottom: 30px;
}

.share-incentive-message .main-message {
    color: #ffffff;
    font-size: 18px;
    font-weight: 600;
    margin: 0 0 15px 0;
}

.share-incentive-message .encouragement-message {
    color: #e0e0e0;
    font-size: 16px;
    line-height: 1.5;
    margin: 0 0 15px 0;
}

.share-incentive-message .encouragement-message strong {
    color: #ffd700;
    font-weight: bold;
}

.share-incentive-message .tip-message {
    color: #ffd700;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
}

.share-incentive-actions {
    margin-top: 25px;
}

.skip-share-btn {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    color: #ffffff;
    border: none;
    border-radius: 10px;
    padding: 12px 24px;
    font-size: 14px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.skip-share-btn:hover {
    background: linear-gradient(145deg, #4b5563, #374151);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

/* Share Section Styles (reuse from settings modal) */
.share-incentive-content .share-section {
    border-top: 1px solid rgba(255, 215, 0, 0.2);
    padding-top: 20px;
    margin-top: 20px;
    margin-bottom: 20px;
}

.share-incentive-content .share-text {
    text-align: center;
    margin-bottom: 16px;
}

.share-incentive-content .share-text p {
    color: #ffd700;
    font-size: 14px;
    margin: 0;
    font-weight: 500;
}

.share-incentive-content .share-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 12px;
    flex-wrap: wrap;
}

.share-incentive-content .share-button {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 40px;
    height: 40px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    background: rgba(255, 215, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    padding: 0;
}

.share-incentive-content .share-button img {
    width: 24px;
    height: 24px;
    filter: brightness(0) saturate(100%) invert(85%) sepia(100%) saturate(348%) hue-rotate(15deg) brightness(101%) contrast(103%);
    transition: filter 0.3s ease;
}

.share-incentive-content .share-button:hover {
    transform: translateY(-2px) scale(1.1);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.share-incentive-content .share-button:active {
    transform: translateY(0) scale(1.05);
}

/* Specific platform colors for share incentive */
.share-incentive-content .share-button.facebook:hover {
    background: rgba(24, 119, 242, 0.2);
    border-color: #1877f2;
}

.share-incentive-content .share-button.facebook:hover img {
    filter: brightness(0) saturate(100%) invert(27%) sepia(96%) saturate(1458%) hue-rotate(213deg) brightness(97%) contrast(98%);
}

.share-incentive-content .share-button.twitter:hover {
    background: rgba(29, 161, 242, 0.2);
    border-color: #1da1f2;
}

.share-incentive-content .share-button.twitter:hover img {
    filter: brightness(0) saturate(100%) invert(58%) sepia(96%) saturate(1458%) hue-rotate(182deg) brightness(97%) contrast(98%);
}

.share-incentive-content .share-button.whatsapp:hover {
    background: rgba(37, 211, 102, 0.2);
    border-color: #25d366;
}

.share-incentive-content .share-button.whatsapp:hover img {
    filter: brightness(0) saturate(100%) invert(64%) sepia(98%) saturate(1458%) hue-rotate(120deg) brightness(97%) contrast(98%);
}

.share-incentive-content .share-button.telegram:hover {
    background: rgba(0, 136, 204, 0.2);
    border-color: #0088cc;
}

.share-incentive-content .share-button.telegram:hover img {
    filter: brightness(0) saturate(100%) invert(58%) sepia(96%) saturate(1458%) hue-rotate(182deg) brightness(80%) contrast(98%);
}

/* Mobile responsive adjustments for share incentive */
@media (max-width: 480px) {
    .share-incentive-content {
        padding: 20px;
        max-width: 95%;
    }

    .share-incentive-header h2 {
        font-size: 20px;
    }

    .share-incentive-icon {
        font-size: 36px;
    }

    .share-incentive-message .main-message {
        font-size: 16px;
    }

    .share-incentive-message .encouragement-message {
        font-size: 14px;
    }

    .share-incentive-content .share-buttons {
        gap: 10px;
    }

    .share-incentive-content .share-button {
        width: 36px;
        height: 36px;
    }

    .share-incentive-content .share-button img {
        width: 20px;
        height: 20px;
    }

    .share-incentive-content .share-text p {
        font-size: 13px;
    }
}

/* Remove default button shadows and optimize touch experience */
button,
.chip-btn,
.action-btn,
.setting-btn,
.start-game-btn,
.rules-button,
.settings-button,
.fullscreen-button,
.home-button,
.audio-button,
.rules-close,
.side-bet-btn,
.restart-btn,
.test-btn,
.audio-toggle-btn,
.continue-anyway-btn,
.remind-later-btn,
.skip-share-btn,
.share-button {
    -webkit-tap-highlight-color: transparent;
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    outline: none;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* Remove focus outline on mobile */
/* Top control button focus styles moved to common-buttons.css */
button:focus,
.chip-btn:focus,
.action-btn:focus,
.setting-btn:focus,
.start-game-btn:focus,
.rules-close:focus,
.side-bet-btn:focus,
.restart-btn:focus,
.test-btn:focus,
.audio-toggle-btn:focus,
.continue-anyway-btn:focus,
.remind-later-btn:focus {
    outline: none;
    box-shadow: none;
}

/* Optimize touch targets for mobile */
@media (max-width: 768px) {
    /* Top control button mobile styles moved to common-buttons.css */
    button,
    .chip-btn,
    .action-btn,
    .setting-btn,
    .side-bet-btn,
    .restart-btn,
    .test-btn,
    .audio-toggle-btn {
        touch-action: manipulation;
    }

    /* Improve button spacing on mobile */
    #betting-controls {
        gap: 10px;
    }

    .chip-tray {
        gap: 5px;
        flex-wrap: nowrap;
        overflow-x: auto;
        justify-content: start;
        padding: 10px 0;
    }

    .betting-action-buttons {
        gap: 15px;
    }

    .action-controls {
        gap: 5px;
    }

    .action-controls .action-btn {
        width: 50px;
        height: 50px;
    }

    .action-controls .btn-icon {
        font-size: 16px;
    }

    /* Special fix for stand button icon on mobile */
    .game-actions-section .action-btn.stand-btn .btn-icon {
        font-size: 14px;
    }

    .action-controls .btn-text {
        font-size: 9px;
        bottom: -20px;
    }



    .setting-options {
        gap: 10px;
    }

    /* Remove any remaining shadows on active/pressed states */
    button:active,
    .chip-btn:active,
    .action-btn:active,
    .setting-btn:active,
    .start-game-btn:active,
    .rules-button:active,
    .settings-button:active,
    .fullscreen-button:active,
    .home-button:active,
    .audio-button:active,
    .rules-close:active,
    .side-bet-btn:active,
    .restart-btn:active,
    .test-btn:active,
    .audio-toggle-btn:active,
    .continue-anyway-btn:active,
    .remind-later-btn:active {
        -webkit-tap-highlight-color: transparent;
        outline: none;
        box-shadow: none;
    }
}
/* Additional Mobile Optimizations */
/* Prevent double-tap zoom on interactive elements */
.casino-container,
.players-area,
.dealer-section,
.bottom-controls,
.chip-tray,
.action-controls,
.bet-circle,
.chip,
.card {
    touch-action: manipulation;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

/* Improve scrolling performance */
.rules-modal,
.rules-body,
.audio-settings-content {
    -webkit-overflow-scrolling: touch;
    scroll-behavior: smooth;
}

/* Remove webkit input styling */
input[type="range"] {
    -webkit-appearance: none;
    appearance: none;
    background: transparent;
    cursor: pointer;
}

/* Optimize for high DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .chip,
    .card,
    .bet-circle {
        image-rendering: -webkit-optimize-contrast;
        image-rendering: crisp-edges;
    }
}

/* Mobile iPhone notch/dynamic island compatibility - only in standalone/fullscreen mode */
@media (max-width: 768px) and (display-mode: standalone) {
    .top-status {
        top: 60px;
    }

    .deck-area {
        top: 60px;
    }
}

/* Fallback for iOS Safari in fullscreen mode */
@media (max-width: 768px) and (-webkit-min-device-pixel-ratio: 2) {
    @supports (padding: max(0px)) {
        .top-status {
            top: max(10px, env(safe-area-inset-top));
        }

        .deck-area {
            top: max(10px, env(safe-area-inset-top));
        }
    }
}



/* Share Modal Styles */
.share-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    backdrop-filter: blur(5px);
}

.share-modal-content {
    background: linear-gradient(145deg, #1e293b, #334155);
    border-radius: 16px;
    padding: 0;
    width: 90%;
    max-width: 400px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.5);
    border: 1px solid rgba(255, 215, 0, 0.3);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-30px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.share-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 24px 16px;
    border-bottom: 1px solid rgba(255, 215, 0, 0.2);
}

.share-modal-title {
    color: #ffd700;
    font-size: 20px;
    font-weight: 600;
    margin: 0;
}

.share-modal-close {
    background: none;
    border: none;
    color: #94a3b8;
    font-size: 24px;
    cursor: pointer;
    padding: 0;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.share-modal-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ffd700;
}

.share-modal-body {
    padding: 24px;
}

.share-text {
    text-align: center;
    margin-bottom: 24px;
}

.share-text p {
    color: #e2e8f0;
    font-size: 16px;
    margin: 0;
    font-weight: 500;
}

.share-buttons {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.share-button-direct {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 50px;
    height: 50px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 50%;
    background: rgba(255, 215, 0, 0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    padding: 0;
}

.share-button-direct img {
    width: 28px;
    height: 28px;
    filter: brightness(0) saturate(100%) invert(85%) sepia(100%) saturate(348%) hue-rotate(15deg) brightness(101%) contrast(103%);
    transition: filter 0.3s ease;
}

.share-button-direct:hover {
    background: rgba(255, 215, 0, 0.2);
    border-color: rgba(255, 215, 0, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.share-button-direct:hover img {
    filter: brightness(0) saturate(100%) invert(100%) sepia(0%) saturate(0%) hue-rotate(0deg) brightness(100%) contrast(100%);
}

/* Mobile responsive styles for share modal */
@media (max-width: 768px) {
    .share-modal-content {
        width: 95%;
        margin: 20px;
    }

    .share-modal-header {
        padding: 16px 20px 12px;
    }

    .share-modal-title {
        font-size: 18px;
    }

    .share-modal-body {
        padding: 20px;
    }

    .share-buttons {
        gap: 12px;
    }

    .share-button-direct {
        width: 45px;
        height: 45px;
    }

    .share-button-direct img {
        width: 24px;
        height: 24px;
    }
}